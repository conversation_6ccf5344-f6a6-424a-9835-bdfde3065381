"""
Handlers package for the Wiz Aroma Delivery Bot.
"""

# Import all handler modules so they can be imported from this package
# These imports are necessary to ensure all handlers are registered with the registry
# The IDE may show these as unused, but they are actually used for their side effects
# (registering handlers with the registry)
from src.handlers import (
    main_handlers,  # noqa
    order_handlers,  # noqa
    admin_handlers,  # noqa
    payment_handlers,  # noqa
    user_profile_handlers,  # noqa
    favorite_orders_handlers,  # noqa
    maintenance_handlers,  # noqa
    location_handlers,  # noqa
    delivery_personnel_handlers,  # noqa
    management_bot_handlers,  # noqa
)

# Import handler registration utilities
from src.utils.handler_registration import get_registry
from src.utils.logging_utils import get_logger

# Get the logger
logger = get_logger()

# Log that handlers are being loaded
logger.info("Loading all handlers")

# The imports above will register all handlers with the registry
# This is because the handler modules use the @register_handler decorator

# Get the registry
registry = get_registry()

# Log the number of handlers registered
user_handlers = len(registry.handlers["user"])
logger.info(f"Registered {user_handlers} user bot handlers")

admin_handlers = len(registry.handlers["admin"])
logger.info(f"Registered {admin_handlers} admin bot handlers")

finance_handlers = len(registry.handlers["finance"])
logger.info(f"Registered {finance_handlers} finance bot handlers")

maintenance_handlers = len(registry.handlers["maintenance"])
logger.info(f"Registered {maintenance_handlers} maintenance bot handlers")

management_handlers = len(registry.handlers.get("management", []))
logger.info(f"Registered {management_handlers} management bot handlers")
