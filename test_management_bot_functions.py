#!/usr/bin/env python3
"""
Test Management Bot Functions without Bot Instance
Tests the data synchronization functions without requiring Telegram bot connection.
"""

import sys
import os
from datetime import datetime

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_data_functions():
    """Test management bot data functions without bot instance"""
    print("🔄 Testing Management Bot Data Functions")
    print("=" * 55)
    
    try:
        # Test 1: Import data functions only
        print("📦 Test 1: Import Data Functions")
        print("-" * 35)
        
        from src.bots.management_bot import (
            refresh_analytics_data,
            refresh_personnel_data,
            refresh_order_data,
            auto_refresh_analytics_data,
            auto_refresh_personnel_data,
            auto_refresh_order_data,
            data_refresh_manager,
            get_real_time_order_status,
            get_real_time_delivery_personnel_status,
            validate_cross_bot_data_consistency,
            get_system_health_status,
            trigger_immediate_refresh
        )
        print("✅ All data functions imported successfully")
        
        # Test 2: Data refresh manager
        print("\n⚙️ Test 2: Data Refresh Manager")
        print("-" * 35)
        
        print(f"Auto-refresh enabled: {data_refresh_manager.auto_refresh_enabled}")
        print(f"Refresh intervals: {data_refresh_manager.refresh_intervals}")
        print(f"Last refresh times: {len(data_refresh_manager.last_refresh_times)} tracked")
        print("✅ Data refresh manager working")
        
        # Test 3: Basic data refresh
        print("\n📊 Test 3: Basic Data Refresh")
        print("-" * 35)
        
        analytics_data = refresh_analytics_data()
        print(f"Analytics collections: {len(analytics_data)}")
        
        personnel_data = refresh_personnel_data()
        print(f"Personnel count: {len(personnel_data)}")
        
        order_data = refresh_order_data()
        print(f"Order collections: {len(order_data)}")
        
        print("✅ Basic data refresh working")
        
        # Test 4: Auto-refresh functions
        print("\n⚡ Test 4: Auto-Refresh Functions")
        print("-" * 35)
        
        auto_analytics = auto_refresh_analytics_data()
        print(f"Auto-analytics collections: {len(auto_analytics)}")
        
        auto_personnel = auto_refresh_personnel_data()
        print(f"Auto-personnel count: {len(auto_personnel)}")
        
        auto_orders = auto_refresh_order_data()
        print(f"Auto-orders collections: {len(auto_orders)}")
        
        print("✅ Auto-refresh functions working")
        
        # Test 5: Real-time status functions
        print("\n📋 Test 5: Real-Time Status Functions")
        print("-" * 35)
        
        order_status = get_real_time_order_status()
        if 'error' in order_status:
            print(f"⚠️ Order status error: {order_status['error']}")
        else:
            stats = order_status.get('statistics', {})
            print(f"Active orders: {stats.get('total_active_orders', 0)}")
            print(f"Pending: {stats.get('pending_assignment_count', 0)}")
            print("✅ Real-time order status working")
        
        personnel_status = get_real_time_delivery_personnel_status()
        if 'error' in personnel_status:
            print(f"⚠️ Personnel status error: {personnel_status['error']}")
        else:
            summary = personnel_status.get('summary', {})
            print(f"Total personnel: {summary.get('total_personnel', 0)}")
            print(f"Available: {summary.get('available', 0)}")
            print("✅ Real-time personnel status working")
        
        # Test 6: Data consistency validation
        print("\n🔍 Test 6: Data Consistency Validation")
        print("-" * 35)
        
        consistency_report = validate_cross_bot_data_consistency()
        if 'error' in consistency_report:
            print(f"⚠️ Consistency validation error: {consistency_report['error']}")
        else:
            issues = consistency_report.get('consistency_issues', [])
            print(f"Consistency issues: {len(issues)}")
            if issues:
                for issue in issues[:2]:
                    print(f"  - {issue}")
            print("✅ Data consistency validation working")
        
        # Test 7: System health monitoring
        print("\n🏥 Test 7: System Health Monitoring")
        print("-" * 35)
        
        health_status = get_system_health_status()
        if 'error' in health_status:
            print(f"⚠️ Health status error: {health_status['error']}")
        else:
            overall_health = health_status.get('overall_health', {})
            print(f"Health score: {overall_health.get('score', 0)}/100")
            print(f"Health grade: {overall_health.get('grade', 'Unknown')}")
            print(f"Health status: {overall_health.get('status', 'unknown')}")
            
            alerts = health_status.get('alerts', [])
            print(f"Active alerts: {len(alerts)}")
            print("✅ System health monitoring working")
        
        # Test 8: Immediate refresh trigger
        print("\n🚀 Test 8: Immediate Refresh Trigger")
        print("-" * 35)
        
        refresh_result = trigger_immediate_refresh()
        if 'error' in refresh_result:
            print(f"⚠️ Immediate refresh error: {refresh_result['error']}")
        else:
            print(f"Refresh status: {refresh_result.get('status', 'unknown')}")
            print(f"Analytics records: {refresh_result.get('analytics_records', 0)}")
            print(f"Personnel count: {refresh_result.get('personnel_count', 0)}")
            print("✅ Immediate refresh trigger working")
        
        # Test 9: Firebase connectivity
        print("\n🔥 Test 9: Firebase Connectivity")
        print("-" * 35)
        
        from src.firebase_db import get_data
        test_personnel = get_data("delivery_personnel") or {}
        test_orders = get_data("confirmed_orders") or {}
        
        print(f"Direct Firebase personnel: {len(test_personnel)}")
        print(f"Direct Firebase orders: {len(test_orders)}")
        print("✅ Firebase connectivity working")
        
        # Test 10: Data comparison
        print("\n📊 Test 10: Data Comparison")
        print("-" * 35)
        
        # Compare management bot data with direct Firebase
        mgmt_personnel_count = len(auto_personnel)
        direct_personnel_count = len(test_personnel)
        
        mgmt_orders_count = len(auto_analytics.get('confirmed_orders', {}))
        direct_orders_count = len(test_orders)
        
        personnel_match = mgmt_personnel_count == direct_personnel_count
        orders_match = mgmt_orders_count == direct_orders_count
        
        print(f"Personnel match: {personnel_match} (Mgmt: {mgmt_personnel_count}, Direct: {direct_personnel_count})")
        print(f"Orders match: {orders_match} (Mgmt: {mgmt_orders_count}, Direct: {direct_orders_count})")
        
        if personnel_match and orders_match:
            print("✅ Management bot data matches Firebase")
        else:
            print("⚠️ Data mismatch - may indicate sync issues")
        
        # Summary
        print("\n" + "=" * 55)
        print("📋 TEST SUMMARY")
        print("=" * 55)
        
        tests_passed = 0
        total_tests = 10
        
        # Simple pass/fail counting
        if analytics_data and personnel_data and order_data:
            tests_passed += 1
        if auto_analytics and auto_personnel and auto_orders:
            tests_passed += 1
        if 'error' not in order_status:
            tests_passed += 1
        if 'error' not in personnel_status:
            tests_passed += 1
        if 'error' not in consistency_report:
            tests_passed += 1
        if 'error' not in health_status:
            tests_passed += 1
        if 'error' not in refresh_result:
            tests_passed += 1
        if test_personnel is not None and test_orders is not None:
            tests_passed += 1
        if personnel_match and orders_match:
            tests_passed += 1
        if data_refresh_manager.auto_refresh_enabled:
            tests_passed += 1
        
        success_rate = (tests_passed / total_tests) * 100
        
        print(f"Tests passed: {tests_passed}/{total_tests}")
        print(f"Success rate: {success_rate:.1f}%")
        
        if success_rate >= 80:
            print("✅ Real-time data synchronization is working excellently!")
            print("🎉 Management bot is ready for production use")
        elif success_rate >= 60:
            print("⚠️ Real-time data synchronization is mostly working")
            print("🔧 Some minor issues may need attention")
        else:
            print("❌ Real-time data synchronization needs significant work")
            print("🚨 Major issues detected")
        
        print(f"\n⏰ Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        return success_rate >= 60
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Critical error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_data_functions()
    sys.exit(0 if success else 1)
