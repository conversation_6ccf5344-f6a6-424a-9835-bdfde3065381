#!/usr/bin/env python3
"""
Basic test for Management Bot Real-Time Data Synchronization
Simple verification of core functionality.
"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_basic_functionality():
    """Test basic management bot functionality"""
    print("🔄 Testing Management Bot Basic Functionality")
    print("=" * 50)
    
    try:
        # Test 1: Basic imports
        print("📦 Test 1: Basic Imports")
        print("-" * 30)
        
        from src.bots.management_bot import (
            refresh_analytics_data,
            refresh_personnel_data,
            auto_refresh_analytics_data,
            data_refresh_manager
        )
        print("✅ Core functions imported successfully")
        
        # Test 2: Data refresh manager
        print("\n⚙️ Test 2: Data Refresh Manager")
        print("-" * 30)
        
        print(f"Auto-refresh enabled: {data_refresh_manager.auto_refresh_enabled}")
        print(f"Refresh intervals: {data_refresh_manager.refresh_intervals}")
        print("✅ Data refresh manager accessible")
        
        # Test 3: Basic data refresh
        print("\n📊 Test 3: Basic Data Refresh")
        print("-" * 30)
        
        analytics_data = refresh_analytics_data()
        print(f"Analytics collections: {len(analytics_data)}")
        
        personnel_data = refresh_personnel_data()
        print(f"Personnel count: {len(personnel_data)}")
        
        print("✅ Basic data refresh working")
        
        # Test 4: Auto-refresh
        print("\n⚡ Test 4: Auto-Refresh")
        print("-" * 30)
        
        auto_analytics = auto_refresh_analytics_data()
        print(f"Auto-refresh analytics collections: {len(auto_analytics)}")
        print("✅ Auto-refresh working")
        
        # Test 5: Firebase connectivity
        print("\n🔥 Test 5: Firebase Connectivity")
        print("-" * 30)
        
        from src.firebase_db import get_data
        test_data = get_data("delivery_personnel") or {}
        print(f"Direct Firebase personnel count: {len(test_data)}")
        print("✅ Firebase connectivity working")
        
        print("\n" + "=" * 50)
        print("✅ ALL BASIC TESTS PASSED")
        print("Management bot core functionality is working")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_basic_functionality()
    if success:
        print("\n🎉 Ready to test advanced features!")
    else:
        print("\n💥 Basic functionality issues need to be resolved first")
    sys.exit(0 if success else 1)
