#!/usr/bin/env python3
"""
Test script for Management Bot Real-Time Data Synchronization
Verifies that management bot data matches live system data and updates in real-time.
"""

import sys
import os
import time
import json
from datetime import datetime

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_real_time_data_sync():
    """Test real-time data synchronization between management bot and live system"""
    print("🔄 Testing Management Bot Real-Time Data Synchronization")
    print("=" * 60)
    
    try:
        # Test imports
        print("📦 Testing imports...")
        from src.bots.management_bot import (
            refresh_analytics_data,
            refresh_personnel_data,
            refresh_order_data,
            auto_refresh_analytics_data,
            auto_refresh_personnel_data,
            auto_refresh_order_data,
            get_real_time_order_status,
            get_real_time_delivery_personnel_status,
            validate_cross_bot_data_consistency,
            sync_management_bot_with_live_system,
            get_system_health_status,
            trigger_immediate_refresh,
            data_refresh_manager
        )
        from src.firebase_db import get_data
        print("✅ All imports successful")
        
        # Test 1: Basic data refresh functions
        print("\n📊 Test 1: Basic Data Refresh Functions")
        print("-" * 40)
        
        analytics_data = refresh_analytics_data()
        personnel_data = refresh_personnel_data()
        order_data = refresh_order_data()
        
        print(f"Analytics data collections: {len(analytics_data)}")
        print(f"Personnel count: {len(personnel_data)}")
        print(f"Order data collections: {len(order_data)}")
        
        # Verify data structure
        expected_analytics_keys = ['completed_orders', 'confirmed_orders', 'assignments', 'earnings', 'personnel']
        missing_keys = [key for key in expected_analytics_keys if key not in analytics_data]
        if missing_keys:
            print(f"⚠️ Missing analytics keys: {missing_keys}")
        else:
            print("✅ Analytics data structure complete")
        
        # Test 2: Auto-refresh mechanisms
        print("\n⚡ Test 2: Auto-Refresh Mechanisms")
        print("-" * 40)
        
        # Test auto-refresh with caching
        start_time = time.time()
        auto_analytics_1 = auto_refresh_analytics_data()
        first_call_time = time.time() - start_time
        
        start_time = time.time()
        auto_analytics_2 = auto_refresh_analytics_data()
        second_call_time = time.time() - start_time
        
        print(f"First auto-refresh call: {first_call_time:.3f}s")
        print(f"Second auto-refresh call: {second_call_time:.3f}s")
        
        if second_call_time < first_call_time:
            print("✅ Caching mechanism working (second call faster)")
        else:
            print("ℹ️ Caching may not be active or data was refreshed")
        
        # Test 3: Real-time order status
        print("\n📋 Test 3: Real-Time Order Status")
        print("-" * 40)
        
        order_status = get_real_time_order_status()
        if 'error' in order_status:
            print(f"❌ Error getting order status: {order_status['error']}")
        else:
            stats = order_status.get('statistics', {})
            print(f"Total active orders: {stats.get('total_active_orders', 0)}")
            print(f"Pending assignment: {stats.get('pending_assignment_count', 0)}")
            print(f"In progress: {stats.get('in_progress_count', 0)}")
            print(f"Completed today: {stats.get('total_completed_today', 0)}")
            print("✅ Real-time order status retrieved")
        
        # Test 4: Real-time personnel status
        print("\n👥 Test 4: Real-Time Personnel Status")
        print("-" * 40)
        
        personnel_status = get_real_time_delivery_personnel_status()
        if 'error' in personnel_status:
            print(f"❌ Error getting personnel status: {personnel_status['error']}")
        else:
            summary = personnel_status.get('summary', {})
            print(f"Total personnel: {summary.get('total_personnel', 0)}")
            print(f"Available: {summary.get('available', 0)}")
            print(f"Busy: {summary.get('busy', 0)}")
            print(f"At capacity: {summary.get('at_capacity', 0)}")
            print(f"Offline: {summary.get('offline', 0)}")
            print("✅ Real-time personnel status retrieved")
        
        # Test 5: Data consistency validation
        print("\n🔍 Test 5: Data Consistency Validation")
        print("-" * 40)
        
        consistency_report = validate_cross_bot_data_consistency()
        if 'error' in consistency_report:
            print(f"❌ Error in consistency validation: {consistency_report['error']}")
        else:
            issues = consistency_report.get('consistency_issues', [])
            print(f"Consistency issues found: {len(issues)}")
            if issues:
                print("Issues:")
                for issue in issues[:3]:  # Show first 3 issues
                    print(f"  - {issue}")
                if len(issues) > 3:
                    print(f"  ... and {len(issues) - 3} more")
            else:
                print("✅ No consistency issues found")
        
        # Test 6: System synchronization
        print("\n🔄 Test 6: System Synchronization")
        print("-" * 40)
        
        sync_report = sync_management_bot_with_live_system()
        if 'error' in sync_report:
            print(f"❌ Error in system sync: {sync_report['error']}")
        else:
            sync_status = sync_report.get('sync_status', 'unknown')
            corrections = sync_report.get('corrections_applied', [])
            print(f"Sync status: {sync_status}")
            print(f"Corrections applied: {len(corrections)}")
            if corrections:
                for correction in corrections[:3]:
                    print(f"  - {correction}")
            print("✅ System synchronization completed")
        
        # Test 7: System health monitoring
        print("\n🏥 Test 7: System Health Monitoring")
        print("-" * 40)
        
        health_status = get_system_health_status()
        if 'error' in health_status:
            print(f"❌ Error getting health status: {health_status['error']}")
        else:
            overall_health = health_status.get('overall_health', {})
            alerts = health_status.get('alerts', [])
            
            print(f"Overall health score: {overall_health.get('score', 0)}/100")
            print(f"Health grade: {overall_health.get('grade', 'Unknown')}")
            print(f"Health status: {overall_health.get('status', 'unknown')}")
            print(f"Active alerts: {len(alerts)}")
            
            critical_alerts = [a for a in alerts if a.get('type') == 'critical']
            if critical_alerts:
                print(f"Critical alerts: {len(critical_alerts)}")
                for alert in critical_alerts[:2]:
                    print(f"  - {alert.get('message', 'Unknown')}")
            
            print("✅ System health monitoring working")
        
        # Test 8: Immediate refresh trigger
        print("\n🚀 Test 8: Immediate Refresh Trigger")
        print("-" * 40)
        
        refresh_result = trigger_immediate_refresh()
        if 'error' in refresh_result:
            print(f"❌ Error in immediate refresh: {refresh_result['error']}")
        else:
            print(f"Refresh status: {refresh_result.get('status', 'unknown')}")
            print(f"Analytics records: {refresh_result.get('analytics_records', 0)}")
            print(f"Personnel count: {refresh_result.get('personnel_count', 0)}")
            print(f"Order records: {refresh_result.get('order_records', 0)}")
            print("✅ Immediate refresh trigger working")
        
        # Test 9: Data freshness comparison
        print("\n📊 Test 9: Data Freshness Comparison")
        print("-" * 40)
        
        # Compare management bot data with direct Firebase data
        direct_confirmed_orders = get_data("confirmed_orders") or {}
        direct_personnel = get_data("delivery_personnel") or {}
        
        mgmt_confirmed_orders = analytics_data.get('confirmed_orders', {})
        mgmt_personnel = personnel_data
        
        confirmed_orders_match = len(direct_confirmed_orders) == len(mgmt_confirmed_orders)
        personnel_match = len(direct_personnel) == len(mgmt_personnel)
        
        print(f"Confirmed orders match: {confirmed_orders_match} (Direct: {len(direct_confirmed_orders)}, Mgmt: {len(mgmt_confirmed_orders)})")
        print(f"Personnel data match: {personnel_match} (Direct: {len(direct_personnel)}, Mgmt: {len(mgmt_personnel)})")
        
        if confirmed_orders_match and personnel_match:
            print("✅ Management bot data matches Firebase data")
        else:
            print("⚠️ Data mismatch detected - may indicate sync issues")
        
        # Test 10: Refresh manager status
        print("\n⚙️ Test 10: Refresh Manager Status")
        print("-" * 40)
        
        print(f"Auto-refresh enabled: {data_refresh_manager.auto_refresh_enabled}")
        print(f"Refresh intervals: {data_refresh_manager.refresh_intervals}")
        print(f"Last refresh times: {len(data_refresh_manager.last_refresh_times)} data types tracked")
        
        # Summary
        print("\n" + "=" * 60)
        print("📋 TEST SUMMARY")
        print("=" * 60)
        
        total_tests = 10
        passed_tests = 0
        
        # Count successful tests (simplified)
        if analytics_data and not missing_keys:
            passed_tests += 1
        if auto_analytics_1:
            passed_tests += 1
        if 'error' not in order_status:
            passed_tests += 1
        if 'error' not in personnel_status:
            passed_tests += 1
        if 'error' not in consistency_report:
            passed_tests += 1
        if 'error' not in sync_report:
            passed_tests += 1
        if 'error' not in health_status:
            passed_tests += 1
        if 'error' not in refresh_result:
            passed_tests += 1
        if confirmed_orders_match and personnel_match:
            passed_tests += 1
        if data_refresh_manager.auto_refresh_enabled:
            passed_tests += 1
        
        success_rate = (passed_tests / total_tests) * 100
        
        print(f"Tests passed: {passed_tests}/{total_tests}")
        print(f"Success rate: {success_rate:.1f}%")
        
        if success_rate >= 80:
            print("✅ Real-time data synchronization is working well")
        elif success_rate >= 60:
            print("⚠️ Real-time data synchronization has some issues")
        else:
            print("❌ Real-time data synchronization needs attention")
        
        print(f"\n⏰ Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        return success_rate >= 80
        
    except Exception as e:
        print(f"❌ Critical error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_real_time_data_sync()
    sys.exit(0 if success else 1)
