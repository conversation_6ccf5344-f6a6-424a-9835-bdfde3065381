"""
Management bot handlers for the Wiz Aroma Delivery Bot.
Contains handlers for management bot functionality using centralized registration.
"""

from telebot import types
from src.utils.handler_registration import register_handler
from src.config import logger
from src.utils.auth import is_admin
from src.utils.rate_limiting import is_rate_limited

# Import management bot functions
from src.bots.management_bot import (
    handle_start as mgmt_handle_start,
    handle_callback_query as mgmt_handle_callback_query,
    handle_backup_personnel as mgmt_handle_backup_personnel,
    backup_delivery_personnel
)


@register_handler("management", "message", commands=['start', 'help'])
def handle_start(message):
    """Handle start and help commands for management bot"""
    try:
        mgmt_handle_start(message)
    except Exception as e:
        logger.error(f"Error in management bot start handler: {e}")


@register_handler("management", "callback_query", func=lambda call: True)
def handle_callback_query(call):
    """Handle callback queries for management bot"""
    try:
        mgmt_handle_callback_query(call)
    except Exception as e:
        logger.error(f"Error in management bot callback handler: {e}")


@register_handler("management", "message", commands=['backup_personnel'])
def handle_backup_personnel(message):
    """Handle backup personnel command for management bot"""
    try:
        mgmt_handle_backup_personnel(message)
    except Exception as e:
        logger.error(f"Error in management bot backup handler: {e}")


# Additional management bot handlers can be added here using the @register_handler decorator
# Example:
# @register_handler("management", "message", commands=['some_command'])
# def handle_some_command(message):
#     """Handle some command"""
#     pass
